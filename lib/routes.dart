import 'package:flutter/material.dart';
import 'presentation/screens/account_settings_screen.dart';
import 'presentation/screens/booking_history_screen.dart';
import 'presentation/screens/booking_screen.dart';
import 'presentation/screens/calendar_screen.dart'; // Import CalendarScreen
import 'presentation/screens/categories_screen.dart';
import 'presentation/screens/chat_detail_screen.dart';
import 'presentation/screens/login_screen.dart';
import 'presentation/screens/map_screen.dart';
import 'presentation/screens/main_screen.dart';
import 'presentation/screens/onboarding_screen.dart';
import 'presentation/screens/otp_verification_screen.dart';
import 'presentation/screens/registration_screen.dart';
import 'presentation/screens/signup_screen.dart';
import 'presentation/screens/splash_screen.dart';
import 'presentation/screens/villa_detail_screen.dart';
import 'presentation/screens/villa_group_screen.dart';
import 'presentation/screens/featured_villa_page.dart';
import 'presentation/screens/nearby_villa_page.dart';
import 'presentation/screens/edit_profile_screen.dart';
import 'presentation/screens/notification_screen.dart';
import 'presentation/screens/search_screen.dart';

class AppRoutes {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String otpVerification = '/otp-verification';
  static const String register = '/register';
  static const String home = '/home';
  static const String main = '/main';
  static const String villaDetail = '/villa-detail';
  static const String villaGroup = '/villa-group';
  static const String booking = '/booking';
  static const String bookingHistory = '/booking-history';
  static const String accountSettings = '/account-settings';
  static const String map = '/map';
  static const String chatDetail = '/chat-detail';
  static const String categories = '/categories';
  static const String calendar =
      CalendarScreen.routeName; // Add calendar route name
  static const String featuredVillas = '/featured-villas';
  static const String nearbyVillas = '/nearby-villas';
  static const String editProfile = '/edit-profile';
  static const String notifications = '/notifications';
  static const String search = '/search';

  static Map<String, WidgetBuilder> getRoutes() {
    return {
      splash: (context) => const SplashScreen(),
      onboarding: (context) => const OnboardingScreen(),
      login: (context) => const LoginScreen(),
      signup: (context) => const SignupScreen(),
      home: (context) => const MainScreen(),
      main: (context) => const MainScreen(),
      accountSettings: (context) => const AccountSettingsScreen(),
      map: (context) => const MapScreen(),
      categories: (context) => const CategoriesScreen(),
      calendar: (context) {
        final villaId = ModalRoute.of(context)?.settings.arguments as String?;
        if (villaId != null) {
          return CalendarScreen(villaId: villaId);
        }
        return const Scaffold(
          body: Center(
            child: Text('Error: Villa ID not provided for CalendarScreen'),
          ),
        );
      },
      booking: (context) {
        // Add BookingScreen route handling
        final args =
            ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
        if (args != null &&
            args.containsKey('villaId') &&
            args.containsKey('fromDate') &&
            args.containsKey('toDate')) {
          final villaId = args['villaId'] as String;
          final fromDate = args['fromDate'] as DateTime;
          final toDate = args['toDate'] as DateTime;
          return BookingScreen(
            villaId: villaId,
            fromDate: fromDate,
            toDate: toDate,
          );
        }
        // Fallback or error handling if arguments are not correct
        return const Scaffold(
          body: Center(
            child: Text('Error: Booking arguments missing or invalid.'),
          ),
        );
      },
      editProfile: (context) => const EditProfileScreen(),
      notifications: (context) => const NotificationScreen(),
      search: (context) => const SearchScreen(),
      // For OTP verification and registration, we'll use a different approach since we need to pass parameters
    };
  }

  // Navigate to map screen
  static Route<dynamic> generateMapRoute(BuildContext context) {
    return MaterialPageRoute(builder: (context) => const MapScreen());
  }

  // Navigate to villa detail with villa ID or villa group based on villaGroupId
  static Route<dynamic> generateVillaDetailRoute(
    BuildContext context,
    String villaId,
  ) {
    return MaterialPageRoute(
      builder: (context) => VillaDetailScreen(villaId: villaId),
    );
  }

  // Smart navigation that checks villaGroupId and redirects accordingly
  static void navigateToVillaOrGroup(
    BuildContext context,
    dynamic villa, {
    String? fromDate,
    String? toDate,
  }) {
    // Check if villa has villaGroupId
    int? villaGroupId;

    if (villa is Map<String, dynamic>) {
      villaGroupId = villa['villaGroupId'] as int?;
    } else if (villa.runtimeType.toString().contains('Villa')) {
      // Handle Villa model
      try {
        villaGroupId = (villa as dynamic).villaGroupId as int?;
      } catch (e) {
        villaGroupId = null;
      }
    } else if (villa.runtimeType.toString().contains('ApiVilla')) {
      // Handle ApiVilla model
      try {
        villaGroupId = (villa as dynamic).villaGroupId as int?;
      } catch (e) {
        villaGroupId = null;
      }
    }

    // Navigate based on villaGroupId
    if (villaGroupId != null && villaGroupId > 0) {
      // Navigate to villa group screen
      Navigator.of(context).push(
        generateVillaGroupRoute(
          context,
          villaGroupId,
          fromDate: fromDate,
          toDate: toDate,
        ),
      );
    } else {
      // Navigate to villa detail screen
      String villaId;
      if (villa is Map<String, dynamic>) {
        villaId = villa['id']?.toString() ?? '';
      } else {
        try {
          villaId = (villa as dynamic).id?.toString() ?? '';
        } catch (e) {
          villaId = '';
        }
      }

      if (villaId.isNotEmpty) {
        Navigator.of(context).push(generateVillaDetailRoute(context, villaId));
      }
    }
  }

  // Navigate to villa group screen
  static Route<dynamic> generateVillaGroupRoute(
    BuildContext context,
    int villaGroupId, {
    String? fromDate,
    String? toDate,
  }) {
    return MaterialPageRoute(
      builder:
          (context) => VillaGroupScreen(
            villaGroupId: villaGroupId,
            fromDate: fromDate,
            toDate: toDate,
          ),
    );
  }

  // Navigate to booking screen with villa ID (and optional dates)
  // This method might need to be updated or deprecated if all navigation to BookingScreen
  // now comes from CalendarScreen with dates.
  static Route<dynamic> generateBookingRoute(
    BuildContext context,
    String villaId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    return MaterialPageRoute(
      builder:
          (context) => BookingScreen(
            villaId: villaId,
            fromDate: fromDate,
            toDate: toDate,
          ),
    );
  }

  // Navigate to booking history screen
  static Route<dynamic> generateBookingHistoryRoute(BuildContext context) {
    return MaterialPageRoute(
      builder: (context) => const BookingHistoryScreen(),
    );
  }

  // Navigate to categories screen
  static Route<dynamic> generateCategoriesRoute(BuildContext context) {
    return MaterialPageRoute(builder: (context) => const CategoriesScreen());
  }

  // Navigate to chat detail screen
  static Route<dynamic> generateChatDetailRoute(
    BuildContext context, {
    required String conversationId,
    required String hostName,
    required String hostAvatar,
    required String villaName,
  }) {
    return MaterialPageRoute(
      builder:
          (context) => ChatDetailScreen(
            conversationId: conversationId,
            hostName: hostName,
            hostAvatar: hostAvatar,
            villaName: villaName,
          ),
    );
  }

  // Navigate to OTP verification screen with contact number
  static Route<dynamic> generateOtpVerificationRoute(
    BuildContext context,
    String contactNumber,
  ) {
    return MaterialPageRoute(
      builder: (context) => OtpVerificationScreen(contactNumber: contactNumber),
    );
  }

  // Navigate to registration screen with contact number
  static Route<dynamic> generateRegistrationRoute(
    BuildContext context,
    String contactNumber,
  ) {
    return MaterialPageRoute(
      builder: (context) => RegistrationScreen(contactNumber: contactNumber),
    );
  }

  // Navigate to featured villas page
  static Route<dynamic> generateFeaturedVillasRoute(BuildContext context) {
    return MaterialPageRoute(builder: (context) => const FeaturedVillaPage());
  }

  // Navigate to nearby villas page
  static Route<dynamic> generateNearbyVillasRoute(BuildContext context) {
    return MaterialPageRoute(builder: (context) => const NearbyVillaPage());
  }

  // Navigate to search screen
  static Route<dynamic> generateSearchRoute(BuildContext context) {
    return MaterialPageRoute(builder: (context) => const SearchScreen());
  }
}
